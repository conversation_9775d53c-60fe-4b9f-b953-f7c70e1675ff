# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema.define(version: 2025_06_18_141314) do

  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_trgm"
  enable_extension "plpgsql"

  create_table "avito_categories", force: :cascade do |t|
    t.string "path"
    t.string "last_attr_name"
    t.integer "sp_cat"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "condition", default: "Новое", null: false
    t.json "fields"
    t.index ["sp_cat"], name: "index_avito_categories_on_sp_cat"
  end

  create_table "barcodes", force: :cascade do |t|
    t.string "code"
    t.string "size"
    t.integer "product_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["code"], name: "index_barcodes_on_code"
    t.index ["product_id"], name: "index_barcodes_on_product_id"
  end

  create_table "brands", force: :cascade do |t|
    t.string "brand_name"
    t.boolean "skip", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "caches", force: :cascade do |t|
    t.string "key", limit: 255
    t.text "data"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["key"], name: "idx_16389_key"
  end

  create_table "category_maps", force: :cascade do |t|
    t.string "cat_type", limit: 255
    t.string "name", limit: 255
  end

  create_table "chat_gpt_batches", force: :cascade do |t|
    t.string "batch_id", null: false
    t.string "status"
    t.datetime "expires_at"
    t.string "filename"
    t.integer "purchase_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "batch_type"
    t.index ["batch_id"], name: "index_chat_gpt_batches_on_batch_id", unique: true
    t.index ["purchase_id"], name: "index_chat_gpt_batches_on_purchase_id"
  end

  create_table "collection_renames", force: :cascade do |t|
    t.string "old_name", limit: 255
    t.string "new_name", limit: 255
    t.bigint "purchase_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["purchase_id"], name: "idx_16419_index_collection_renames_on_purchase_id"
  end

  create_table "collections", force: :cascade do |t|
    t.string "name", limit: 255
    t.string "coltype", limit: 255
    t.bigint "purchase_id"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.text "pics"
    t.boolean "disabled"
    t.boolean "exporting"
    t.bigint "pos"
    t.json "tags2"
    t.string "grp"
    t.jsonb "size_pics"
    t.text "description"
    t.index ["name"], name: "index_collections_on_name"
    t.index ["purchase_id"], name: "idx_16407_purchase_id"
  end

  create_table "collections_products", id: false, force: :cascade do |t|
    t.bigint "collection_id", null: false
    t.bigint "product_id", null: false
    t.index ["collection_id", "product_id"], name: "idx_16414_index_collections_products_on_collection_id_and_produ", unique: true
    t.index ["collection_id"], name: "idx_16414_index_collections_products_on_collection_id"
    t.index ["product_id"], name: "idx_16414_index_collections_products_on_product_id"
  end

  create_table "common_pp_orders", force: :cascade do |t|
    t.integer "sp_order_id"
    t.integer "sp_megaorder_id"
    t.integer "sp_gid"
    t.decimal "price", precision: 8, scale: 2
    t.string "sp_user"
    t.string "name"
    t.string "size"
    t.string "sku"
    t.string "source"
    t.string "provider"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "order_number"
    t.string "invoice_name"
    t.datetime "invoice_processed_at"
    t.string "provider_order_number"
    t.index ["name"], name: "index_common_pp_orders_on_name"
    t.index ["order_number"], name: "index_common_pp_orders_on_order_number"
    t.index ["size"], name: "index_common_pp_orders_on_size"
    t.index ["sku"], name: "index_common_pp_orders_on_sku"
    t.index ["sp_gid"], name: "index_common_pp_orders_on_sp_gid"
    t.index ["sp_megaorder_id"], name: "index_common_pp_orders_on_sp_megaorder_id"
    t.index ["sp_order_id"], name: "index_common_pp_orders_on_sp_order_id"
  end

  create_table "companies", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "delayed_jobs", force: :cascade do |t|
    t.bigint "priority", default: 0, null: false
    t.bigint "attempts", default: 0, null: false
    t.text "handler", null: false
    t.text "last_error"
    t.datetime "run_at"
    t.datetime "locked_at"
    t.datetime "failed_at"
    t.string "locked_by", limit: 255
    t.string "queue", limit: 255
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["priority", "run_at"], name: "idx_16428_delayed_jobs_priority"
  end

  create_table "external_barcodes", force: :cascade do |t|
    t.string "barcode"
    t.string "entity_id"
    t.string "supplier"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["barcode"], name: "index_external_barcodes_on_barcode"
    t.index ["supplier"], name: "index_external_barcodes_on_supplier"
  end

  create_table "inventory_document_lines", force: :cascade do |t|
    t.bigint "product_id"
    t.bigint "amount_change"
    t.string "size", limit: 255
    t.string "comment", limit: 255
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.decimal "buy_price", precision: 10, scale: 2
    t.decimal "sp_price", precision: 10, scale: 2
    t.decimal "retail_price", precision: 10, scale: 2
    t.bigint "inventory_document_id", null: false
    t.bigint "line_type", null: false
    t.json "prev_price_data"
    t.integer "old_amount"
    t.boolean "checked"
    t.string "name"
    t.integer "inventory_product_id"
    t.decimal "rrp", precision: 10, scale: 2
    t.json "transit_info"
    t.index ["inventory_product_id"], name: "index_inventory_document_lines_on_inventory_product_id"
    t.index ["product_id"], name: "idx_16446_index_inventory_document_lines_on_product_id"
  end

  create_table "inventory_documents", force: :cascade do |t|
    t.bigint "doc_type"
    t.bigint "user_id"
    t.string "comment", limit: 255
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "posted", default: false, null: false
    t.string "name"
    t.datetime "posted_at"
    t.integer "megaorder_id"
    t.index ["user_id"], name: "idx_16439_index_inventory_documents_on_user_id"
  end

  create_table "inventory_folders", force: :cascade do |t|
    t.string "name"
    t.integer "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "sp_pid", default: 0, null: false
    t.index ["user_id"], name: "index_inventory_folders_on_user_id"
  end

  create_table "inventory_pictures", force: :cascade do |t|
    t.integer "inventory_product_id"
    t.string "path"
    t.integer "image_type"
    t.integer "width"
    t.integer "height"
    t.integer "file_size"
    t.string "source"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "datahash"
    t.index ["inventory_product_id"], name: "index_inventory_pictures_on_inventory_product_id"
    t.index ["path"], name: "index_inventory_pictures_on_path"
  end

  create_table "inventory_products", force: :cascade do |t|
    t.string "sku", null: false
    t.string "name", null: false
    t.string "desc"
    t.integer "parent_id"
    t.integer "pos"
    t.string "supplier_sku"
    t.string "source"
    t.integer "user_id", null: false
    t.integer "inventory_folder_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "stock"
    t.integer "cat"
    t.string "brand"
    t.jsonb "extra"
    t.index "((stock -> 'barcode'::text))", name: "inventory_products_trgm_barcode", using: :gin
    t.index "((stock)::text) gin_trgm_ops", name: "inventory_products_trgm_stock", using: :gin
    t.index ["inventory_folder_id"], name: "index_inventory_products_on_inventory_folder_id"
    t.index ["name"], name: "index_inventory_products_on_name"
    t.index ["name"], name: "inventory_products_trgm_name", opclass: :gin_trgm_ops, using: :gin
    t.index ["parent_id"], name: "index_inventory_products_on_parent_id"
    t.index ["sku"], name: "index_inventory_products_on_sku", unique: true
    t.index ["sku"], name: "inventory_products_trgm_sku", opclass: :gin_trgm_ops, using: :gin
    t.index ["supplier_sku"], name: "index_inventory_products_on_supplier_sku"
    t.index ["supplier_sku"], name: "inventory_products_trgm_supplier_sku", opclass: :gin_trgm_ops, using: :gin
    t.index ["user_id"], name: "index_inventory_products_on_user_id"
  end

  create_table "ivanovo_orders", force: :cascade do |t|
    t.integer "ivanovo_order_number"
    t.integer "sp_order_id"
    t.integer "sp_megaorder_id"
    t.integer "sp_gid"
    t.decimal "price", precision: 8, scale: 2
    t.string "sp_user"
    t.string "name"
    t.string "size"
    t.string "sku"
    t.string "source"
    t.index ["ivanovo_order_number"], name: "index_ivanovo_orders_on_ivanovo_order_number"
    t.index ["name"], name: "index_ivanovo_orders_on_name"
    t.index ["size"], name: "index_ivanovo_orders_on_size"
    t.index ["sku"], name: "index_ivanovo_orders_on_sku"
    t.index ["sp_gid"], name: "index_ivanovo_orders_on_sp_gid"
    t.index ["sp_megaorder_id"], name: "index_ivanovo_orders_on_sp_megaorder_id"
    t.index ["sp_order_id"], name: "index_ivanovo_orders_on_sp_order_id"
  end

  create_table "pack_slots", force: :cascade do |t|
    t.string "name"
    t.string "state"
    t.integer "megaorder_id"
    t.string "packer"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "pictures", force: :cascade do |t|
    t.bigint "product_id"
    t.text "path"
    t.bigint "image_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "width"
    t.bigint "height"
    t.bigint "file_size"
    t.text "source"
    t.text "datahash"
    t.string "version"
    t.integer "show_order"
    t.index ["path"], name: "index_pictures_on_path"
    t.index ["product_id"], name: "idx_16455_index_pictures_on_product_id"
    t.index ["source"], name: "index_pictures_on_source"
  end

  create_table "pictures_products", id: false, force: :cascade do |t|
    t.bigint "product_id", null: false
    t.bigint "picture_id", null: false
    t.index ["picture_id", "product_id"], name: "index_pictures_products_on_picture_id_and_product_id"
    t.index ["product_id", "picture_id"], name: "index_pictures_products_on_product_id_and_picture_id"
  end

# Could not dump table "pp_discounts" because of following StandardError
#   Unknown type 'discount_type' for column 'disc_type'

  create_table "pp_price_log", id: false, force: :cascade do |t|
    t.integer "gid"
    t.decimal "price_old", precision: 7, scale: 2
    t.decimal "price_new", precision: 7, scale: 2
    t.datetime "created_at", default: -> { "CURRENT_TIMESTAMP" }
  end

  create_table "price_histories", force: :cascade do |t|
    t.integer "product_id"
    t.string "size"
    t.decimal "price"
    t.datetime "saved_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "product_game_infos", force: :cascade do |t|
    t.integer "gid"
    t.integer "cid"
    t.integer "pid"
    t.string "old_image"
    t.datetime "sticker_add_time"
    t.datetime "sticker_remove_time"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "product_news", force: :cascade do |t|
    t.string "cat", limit: 255
    t.string "cat_type", limit: 255
    t.string "sku", limit: 255
    t.text "desc"
    t.text "colors"
    t.decimal "price", precision: 10
    t.string "url", limit: 255
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "name", limit: 255
    t.bigint "purchase_id", null: false
    t.text "sizes_table"
    t.string "producer", limit: 255
    t.string "composition", limit: 255
    t.string "sizes_link", limit: 255
    t.text "additional"
    t.index ["purchase_id", "sku"], name: "idx_16473_index_product_news_on_purchase_id_and_sku"
    t.index ["purchase_id", "url"], name: "idx_16473_index_product_news_on_purchase_id_and_url"
    t.index ["purchase_id"], name: "idx_16473_index_product_news_on_purchase_id"
    t.index ["sku"], name: "idx_16473_index_product_news_on_sku"
  end

  create_table "products", force: :cascade do |t|
    t.string "art", limit: 255
    t.string "name", limit: 255
    t.string "price", limit: 255
    t.text "desc"
    t.text "sizes"
    t.text "pics"
    t.bigint "collection_id"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.text "error"
    t.text "outer_code"
    t.text "extra"
    t.bigint "category"
    t.boolean "disabled"
    t.string "internal_key", limit: 255
    t.string "rrp", limit: 255
    t.string "barcode", limit: 255
    t.decimal "retail_price", precision: 10, scale: 2
    t.decimal "buy_price", precision: 10, scale: 2
    t.bigint "purchase_id"
    t.string "source", limit: 2048
    t.boolean "sale"
    t.json "stock"
    t.string "sku"
    t.integer "brand_id"
    t.string "brand_name"
    t.integer "pos", default: 1000000000, null: false
    t.datetime "downloaded_at"
    t.integer "guess_cat"
    t.integer "sima_cat"
    t.string "edited_desc"
    t.string "edited_name"
    t.boolean "need_reupload_pics", default: false, null: false
    t.integer "box_width"
    t.integer "box_height"
    t.integer "box_depth"
    t.integer "width"
    t.integer "height"
    t.integer "depth"
    t.integer "weight"
    t.string "fixed_fields"
    t.decimal "special_price"
    t.date "special_price_active_until"
    t.integer "old_cid"
    t.decimal "cheapest_sp_price"
    t.string "video_link"
    t.index "((art)::text), purchase_id", name: "idx_combined_on_art_and_purchase_id"
    t.index ["art"], name: "idx_16464_index_products_on_art"
    t.index ["art"], name: "products_trgm_art", opclass: :gin_trgm_ops, using: :gin
    t.index ["barcode"], name: "idx_16464_index_products_on_barcode"
    t.index ["barcode"], name: "products_trgm_barcode", opclass: :gin_trgm_ops, using: :gin
    t.index ["brand_name"], name: "index_products_on_brand_name"
    t.index ["collection_id"], name: "idx_16464_collection_id"
    t.index ["extra"], name: "index_products_on_extra"
    t.index ["internal_key"], name: "idx_16464_index_products_on_internal_key"
    t.index ["name"], name: "index_products_on_name"
    t.index ["name"], name: "products_trgm_name", opclass: :gin_trgm_ops, using: :gin
    t.index ["purchase_id"], name: "idx_16464_index_products_on_purchase_id"
    t.index ["sku"], name: "index_products_on_sku", unique: true
    t.index ["sku"], name: "products_trgm_sku", opclass: :gin_trgm_ops, using: :gin
    t.index ["updated_at"], name: "index_products_on_updated_at"
  end

  create_table "purchases", force: :cascade do |t|
    t.string "name", limit: 255
    t.datetime "downloaded_at"
    t.bigint "user_id"
    t.string "dlclass", limit: 255
    t.datetime "created_at"
    t.datetime "updated_at"
    t.bigint "status"
    t.bigint "progress"
    t.bigint "total_products"
    t.text "message"
    t.boolean "stop"
    t.text "data"
    t.bigint "purchase_id"
    t.string "orderclass", limit: 255
    t.text "cookies"
    t.string "pics", limit: 255
    t.boolean "is_tool", default: false, null: false
    t.string "sp_pid"
    t.text "info"
    t.integer "sp_brand_id"
    t.string "sp_brand_name"
    t.json "purchase_data"
    t.text "code"
    t.integer "company_id"
    t.integer "dev_state"
    t.integer "developer_id"
    t.text "skip_brands"
    t.integer "work_state"
    t.string "mega_id"
    t.datetime "download_started_at"
    t.boolean "error", default: false, null: false
    t.string "collection_tags"
    t.text "cat_rules"
    t.string "cat_model"
    t.boolean "is_pp", default: false, null: false
    t.integer "priority", default: 100, null: false
    t.string "invoice_processor"
    t.datetime "download_finished_at", default: "1970-01-01 00:00:00", null: false
    t.integer "update_period"
    t.integer "discount"
    t.date "discount_until"
    t.integer "sale_cid"
    t.index ["purchase_id"], name: "idx_16482_index_purchases_on_purchase_id"
  end

  create_table "rename_rules", force: :cascade do |t|
    t.integer "collection_id"
    t.integer "purchase_id"
    t.jsonb "rename_data"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["collection_id"], name: "index_rename_rules_on_collection_id"
    t.index ["purchase_id"], name: "index_rename_rules_on_purchase_id"
  end

  create_table "sent_orders", force: :cascade do |t|
    t.bigint "pid", null: false
    t.string "skip_mids", limit: 255
    t.bigint "purchase_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["pid"], name: "idx_16494_index_sent_orders_on_pid"
    t.index ["purchase_id"], name: "idx_16494_index_sent_orders_on_purchase_id"
  end

  create_table "shipment_box_items", force: :cascade do |t|
    t.integer "shipment_box_id"
    t.string "sku"
    t.string "name"
    t.integer "quantity"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["shipment_box_id"], name: "index_shipment_box_items_on_shipment_box_id"
    t.index ["sku"], name: "index_shipment_box_items_on_sku"
  end

  create_table "shipment_boxes", force: :cascade do |t|
    t.string "box_name"
    t.string "barcode"
    t.string "provider"
    t.string "shipment_name"
    t.datetime "received_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["barcode"], name: "index_shipment_boxes_on_barcode"
    t.index ["provider"], name: "index_shipment_boxes_on_provider"
    t.index ["shipment_name"], name: "index_shipment_boxes_on_shipment_name"
  end

  create_table "shipment_items", force: :cascade do |t|
    t.bigint "product_id"
    t.bigint "shipment_id"
    t.decimal "invoice_price", precision: 10, scale: 2
    t.bigint "amount"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["product_id"], name: "idx_16507_index_shipment_items_on_product_id"
    t.index ["shipment_id"], name: "idx_16507_index_shipment_items_on_shipment_id"
  end

  create_table "shipments", force: :cascade do |t|
    t.bigint "purchase_id"
    t.string "title", limit: 255
    t.date "expected_arrival"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "status", default: 0
    t.index ["purchase_id"], name: "idx_16500_index_shipments_on_purchase_id"
  end

  create_table "sima_categories", force: :cascade do |t|
    t.string "name"
    t.integer "parent_id"
    t.string "path"
    t.integer "sp_cat"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "item_count"
    t.boolean "present_in_db", default: false, null: false
    t.string "slug"
    t.index ["parent_id"], name: "index_sima_categories_on_parent_id"
    t.index ["present_in_db"], name: "index_sima_categories_on_present_in_db"
  end

  create_table "sima_check_items", force: :cascade do |t|
    t.bigint "item_id", null: false
    t.integer "sima_check_id", null: false
    t.string "name"
    t.integer "sid", null: false
    t.integer "sima_item_id", null: false
    t.decimal "price", precision: 10, scale: 2
    t.integer "amount"
    t.decimal "shipping_cost", precision: 6, scale: 2
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["item_id"], name: "index_sima_check_items_on_item_id"
    t.index ["sid"], name: "index_sima_check_items_on_sid"
    t.index ["sima_check_id"], name: "index_sima_check_items_on_sima_check_id"
    t.index ["sima_item_id"], name: "index_sima_check_items_on_sima_item_id"
  end

  create_table "sima_checks", force: :cascade do |t|
    t.integer "check_id"
    t.string "check_name"
    t.decimal "goods_price"
    t.decimal "shipping_price"
    t.datetime "check_date"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "sima_orders", force: :cascade do |t|
    t.integer "sima_order_id"
    t.integer "sp_order_id"
    t.integer "sp_megaorder_id"
    t.integer "sp_gid"
    t.string "sp_user"
    t.string "sima_sid"
    t.string "sp_sku"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "invoice_name"
    t.datetime "invoice_processed_at"
    t.index ["sima_order_id"], name: "index_sima_orders_on_sima_order_id"
    t.index ["sima_sid"], name: "index_sima_orders_on_sima_sid"
    t.index ["sp_gid"], name: "index_sima_orders_on_sp_gid"
    t.index ["sp_megaorder_id"], name: "index_sima_orders_on_sp_megaorder_id"
    t.index ["sp_order_id"], name: "index_sima_orders_on_sp_order_id"
    t.index ["sp_sku"], name: "index_sima_orders_on_sp_sku"
  end

  create_table "sima_products", force: :cascade do |t|
    t.bigint "sid"
    t.jsonb "data"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.datetime "downloaded_at"
    t.boolean "disabled", default: false
    t.decimal "buy_price"
    t.decimal "rrp"
    t.decimal "wholesale_price"
    t.decimal "shipping_cost"
    t.index "(((data -> 'trademark'::text) -> 'slug'::text))", name: "index_sima_products_on_trademark_slug", using: :gin
    t.index "((data -> 'all_categories'::text))", name: "index_sima_products_on_all_categories", using: :gin
    t.index "((data ->> 'parent_item_id'::text))", name: "index_sima_products_on_data_parent_item_id"
    t.index ["disabled"], name: "index_sima_products_on_disabled"
    t.index ["sid"], name: "index_sima_products_on_sid", unique: true
  end

  create_table "sima_series", force: :cascade do |t|
    t.integer "sima_id"
    t.string "name"
    t.string "slug"
    t.string "page_title"
    t.string "page_description"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["name"], name: "index_sima_series_on_name"
    t.index ["sima_id"], name: "index_sima_series_on_sima_id"
  end

  create_table "sima_transits", force: :cascade do |t|
    t.string "art"
    t.integer "amount"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "invoice"
    t.integer "sp_megaorder_id"
    t.decimal "buy_price"
    t.datetime "arrived_at"
    t.index ["art"], name: "index_sima_transits_on_art"
  end

  create_table "sp_brands", force: :cascade do |t|
    t.string "name"
    t.integer "sp_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "aliases"
    t.boolean "global", default: false, null: false
    t.index ["name"], name: "index_sp_brands_on_name"
    t.index ["sp_id"], name: "index_sp_brands_on_sp_id"
  end

  create_table "sp_bulletins", force: :cascade do |t|
    t.integer "sp_id"
    t.string "name"
    t.text "description"
    t.string "size"
    t.integer "price"
    t.integer "quantity"
    t.jsonb "pics"
    t.boolean "disabled", default: false, null: false
    t.boolean "used", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["sp_id"], name: "index_sp_bulletins_on_sp_id", unique: true
  end

  create_table "sp_categories", force: :cascade do |t|
    t.string "name"
    t.string "path"
    t.integer "parent_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "avito_category_id"
    t.index ["parent_id"], name: "index_sp_categories_on_parent_id"
  end

  create_table "sp_orders", force: :cascade do |t|
    t.integer "order_id", null: false
    t.integer "megaorder_id", null: false
    t.integer "gid"
    t.string "sku", null: false
    t.integer "slot_id"
    t.string "state"
    t.string "user"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "used_slot_id"
    t.string "processed_by_user"
    t.datetime "placed_at"
    t.datetime "completed_at"
    t.integer "pid"
    t.string "product_name"
    t.decimal "payment_sum", precision: 10, scale: 2
    t.integer "sp_user_id"
    t.index ["megaorder_id"], name: "index_sp_orders_on_megaorder_id"
    t.index ["order_id"], name: "index_sp_orders_on_order_id"
    t.index ["sku"], name: "index_sp_orders_on_sku"
    t.index ["slot_id"], name: "index_sp_orders_on_slot_id"
    t.index ["used_slot_id"], name: "index_sp_orders_on_used_slot_id"
  end

  create_table "sp_products", force: :cascade do |t|
    t.integer "gid"
    t.integer "cid"
    t.string "col_name"
    t.string "sku"
    t.string "name"
    t.string "sizes"
    t.decimal "price"
    t.decimal "rrp"
    t.string "description"
    t.integer "pid"
    t.integer "sp_category_id"
    t.string "brand"
    t.boolean "disabled", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.jsonb "pictures"
    t.integer "sp_fee", default: 0, null: false
    t.decimal "fixed_price"
    t.decimal "fixed_rrp"
    t.datetime "fixed_price_until"
    t.integer "sp_purchase_id"
    t.json "avito_data"
    t.index ["gid"], name: "index_sp_products_on_gid"
    t.index ["pid"], name: "index_sp_products_on_pid"
    t.index ["sku"], name: "index_sp_products_on_sku"
  end

  create_table "sp_purchase_data", force: :cascade do |t|
    t.integer "oid"
    t.integer "gid"
    t.string "art"
    t.string "name"
    t.integer "sp_purchase_id"
    t.string "purchase_name"
    t.string "collection_name"
    t.string "user_name"
    t.string "status"
    t.string "user_order_comment"
    t.string "org_order_comment"
    t.string "org_user_comment"
    t.string "org_megaorder_comment"
    t.integer "distributor_id"
    t.integer "megaorder_id"
    t.string "distributor_name"
    t.string "is_finished"
    t.string "finished"
    t.integer "megapurchase_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["art"], name: "index_sp_purchase_data_on_art"
    t.index ["oid"], name: "index_sp_purchase_data_on_oid", unique: true
    t.index ["sp_purchase_id"], name: "index_sp_purchase_data_on_sp_purchase_id"
  end

  create_table "sp_purchases", force: :cascade do |t|
    t.integer "pid"
    t.integer "mid"
    t.string "name"
    t.string "state"
    t.datetime "last_up"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "user_id"
    t.integer "company_id"
    t.integer "purchase_id"
    t.string "variant"
    t.boolean "skip_price_updates", default: false, null: false
    t.datetime "skip_price_updates_until", default: -> { "CURRENT_TIMESTAMP" }, null: false
    t.datetime "last_tg_message"
    t.integer "delivery_days"
    t.datetime "close_time"
    t.index ["last_up"], name: "index_sp_purchases_on_last_up"
    t.index ["mid"], name: "index_sp_purchases_on_mid"
    t.index ["pid"], name: "index_sp_purchases_on_pid"
    t.index ["state"], name: "index_sp_purchases_on_state"
  end

  create_table "taggings", force: :cascade do |t|
    t.bigint "tag_id"
    t.bigint "taggable_id"
    t.string "taggable_type", limit: 255
    t.bigint "tagger_id"
    t.string "tagger_type", limit: 255
    t.string "context", limit: 128
    t.datetime "created_at"
    t.index ["context"], name: "idx_16513_index_taggings_on_context"
    t.index ["tag_id", "taggable_id", "taggable_type", "context", "tagger_id", "tagger_type"], name: "idx_16513_taggings_idx", unique: true
    t.index ["tag_id"], name: "idx_16513_index_taggings_on_tag_id"
    t.index ["taggable_id", "taggable_type", "context"], name: "idx_16513_index_taggings_on_taggable_id_and_taggable_type_and_c"
    t.index ["taggable_id", "taggable_type", "tagger_id", "context"], name: "idx_16513_taggings_idy"
    t.index ["taggable_id"], name: "idx_16513_index_taggings_on_taggable_id"
    t.index ["taggable_type"], name: "idx_16513_index_taggings_on_taggable_type"
    t.index ["tagger_id", "tagger_type"], name: "idx_16513_index_taggings_on_tagger_id_and_tagger_type"
    t.index ["tagger_id"], name: "idx_16513_index_taggings_on_tagger_id"
  end

  create_table "tags", force: :cascade do |t|
    t.string "name", limit: 255
    t.bigint "taggings_count", default: 0
    t.datetime "created_at", default: -> { "CURRENT_TIMESTAMP" }, null: false
    t.datetime "updated_at", default: -> { "CURRENT_TIMESTAMP" }, null: false
    t.index ["name"], name: "idx_16522_index_tags_on_name", unique: true
  end

  create_table "text_data", force: :cascade do |t|
    t.text "text", null: false
    t.string "tag", null: false
    t.index ["tag"], name: "index_text_data_on_tag"
  end

  create_table "uploads", force: :cascade do |t|
    t.string "pid", limit: 255
    t.datetime "uploaded_at"
    t.bigint "user_id"
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", limit: 255, default: "", null: false
    t.string "encrypted_password", limit: 255, default: "", null: false
    t.string "reset_password_token", limit: 255
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.bigint "sign_in_count", default: 0
    t.datetime "current_sign_in_at"
    t.datetime "last_sign_in_at"
    t.string "current_sign_in_ip", limit: 255
    t.string "last_sign_in_ip", limit: 255
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "role"
    t.integer "owner_id"
    t.integer "company_id"
    t.integer "developer_id"
    t.string "jti", null: false
    t.index ["email"], name: "idx_16535_index_users_on_email", unique: true
    t.index ["jti"], name: "index_users_on_jti", unique: true
    t.index ["reset_password_token"], name: "idx_16535_index_users_on_reset_password_token", unique: true
  end

  add_foreign_key "collection_renames", "purchases", on_update: :restrict, on_delete: :restrict
  add_foreign_key "inventory_document_lines", "products", on_update: :restrict, on_delete: :restrict
  add_foreign_key "inventory_documents", "users", on_update: :restrict, on_delete: :restrict
  add_foreign_key "pictures", "products", on_update: :restrict, on_delete: :restrict
  add_foreign_key "purchases", "purchases", on_update: :restrict, on_delete: :restrict
  add_foreign_key "sent_orders", "purchases", on_update: :restrict, on_delete: :restrict
  add_foreign_key "shipment_items", "products", on_update: :restrict, on_delete: :restrict
  add_foreign_key "shipment_items", "shipments", on_update: :restrict, on_delete: :restrict
  add_foreign_key "shipments", "purchases", on_update: :restrict, on_delete: :restrict
end
