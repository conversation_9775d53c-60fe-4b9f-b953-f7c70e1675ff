# frozen_string_literal: true

class AvitoCategoryMatcher
  
  def initialize
    @data_loader = AvitoDataLoader.instance
  end
  
  # Find the best matching Avito category for a product using LLM
  def find_best_category(product_name, product_description = nil, options = {})
    ensure_data_loaded
    
    # Get initial suggestions based on keyword matching
    suggestions = @data_loader.suggest_categories_for_product(
      product_name, 
      product_description, 
      limit: options[:suggestion_limit] || 10
    )
    
    if suggestions.empty?
      Rails.logger.warn "No category suggestions found for product: #{product_name}"
      return nil
    end
    
    # Use LLM to make the final decision
    if options[:use_llm] != false
      llm_result = ask_llm_for_category(product_name, product_description, suggestions)
      return llm_result if llm_result
    end
    
    # Fallback to highest scored suggestion
    suggestions.first
  end
  
  # Batch process multiple products
  def batch_categorize(products, options = {})
    ensure_data_loaded
    
    results = []
    
    products.each_with_index do |product, index|
      Rails.logger.info "Processing product #{index + 1}/#{products.count}: #{product[:name]}"
      
      result = find_best_category(
        product[:name], 
        product[:description], 
        options
      )
      
      results << {
        product: product,
        category_match: result,
        processed_at: Time.current
      }
      
      # Add delay to avoid overwhelming LLM API
      sleep(options[:delay] || 0.5) if options[:use_llm] != false
    end
    
    results
  end
  
  # Get category mapping for SP categories
  def map_sp_category_to_avito(sp_category_id, sp_category_name = nil)
    ensure_data_loaded
    
    # Check if we already have a mapping in the database
    existing_mapping = AvitoCategory.find_by(sp_cat: sp_category_id)
    return format_existing_mapping(existing_mapping) if existing_mapping
    
    # Use category name to find suggestions
    if sp_category_name.present?
      suggestions = @data_loader.search_categories_by_name(sp_category_name, limit: 5)
      
      if suggestions.any?
        return {
          category: suggestions.first,
          attributes: @data_loader.get_category_attributes(suggestions.first[:id]),
          confidence: :high,
          source: :name_match
        }
      end
    end
    
    nil
  end
  
  # Validate category assignment
  def validate_category_assignment(category_id, product_name, product_description = nil)
    ensure_data_loaded
    
    category = @data_loader.get_category(category_id)
    return { valid: false, reason: 'Category not found' } unless category
    
    attributes = @data_loader.get_category_attributes(category_id)
    return { valid: false, reason: 'Category attributes not found' } unless attributes
    
    # Check if product seems to fit the category
    category_keywords = extract_category_keywords(category, attributes)
    product_keywords = extract_product_keywords(product_name, product_description)
    
    overlap = (category_keywords & product_keywords).count
    confidence = overlap.to_f / [category_keywords.count, product_keywords.count].max
    
    {
      valid: confidence > 0.1,
      confidence: confidence,
      category: category,
      attributes: attributes,
      matching_keywords: category_keywords & product_keywords
    }
  end
  
  # Get required attributes for a category
  def get_required_attributes(category_id)
    ensure_data_loaded
    
    attributes = @data_loader.get_category_attributes(category_id)
    return [] unless attributes&.dig('attributes')
    
    required_attrs = []
    
    attributes['attributes'].each do |attr|
      if attr['conditions']&.include?('Обязательный')
        required_attrs << {
          tag_name: attr['tag_name'],
          description: attr['description'],
          value_type: attr['value_type'],
          values: attr['values'],
          example: attr['example']
        }
      end
    end
    
    required_attrs
  end
  
  # Generate category assignment report
  def generate_assignment_report(assignments)
    report = {
      total_products: assignments.count,
      successfully_categorized: 0,
      failed_categorizations: 0,
      categories_used: {},
      confidence_distribution: { high: 0, medium: 0, low: 0 },
      generated_at: Time.current
    }
    
    assignments.each do |assignment|
      if assignment[:category_match]
        report[:successfully_categorized] += 1
        
        category_id = assignment[:category_match][:category][:id]
        report[:categories_used][category_id] ||= 0
        report[:categories_used][category_id] += 1
        
        # Categorize confidence
        score = assignment[:category_match][:score] || 0
        if score > 20
          report[:confidence_distribution][:high] += 1
        elsif score > 10
          report[:confidence_distribution][:medium] += 1
        else
          report[:confidence_distribution][:low] += 1
        end
      else
        report[:failed_categorizations] += 1
      end
    end
    
    report[:success_rate] = (report[:successfully_categorized].to_f / report[:total_products] * 100).round(2)
    
    report
  end
  
  private
  
  def ensure_data_loaded
    @data_loader.ensure_fresh_data
  end
  
  def ask_llm_for_category(product_name, product_description, suggestions)
    # This method would integrate with your LLM service
    # For now, returning the top suggestion
    # You can implement actual LLM integration here
    
    Rails.logger.info "LLM integration not implemented yet, using top suggestion"
    suggestions.first
  end
  
  def format_existing_mapping(avito_category)
    category_data = @data_loader.get_category(avito_category.sp_cat.to_s)
    attributes_data = @data_loader.get_category_attributes(avito_category.sp_cat.to_s)
    
    {
      category: category_data,
      attributes: attributes_data,
      confidence: :existing_mapping,
      source: :database,
      avito_category_record: avito_category
    }
  end
  
  def extract_category_keywords(category, attributes)
    keywords = []
    
    # Extract from category name and path
    text = [category[:name], category[:full_path]].join(' ')
    keywords.concat(extract_keywords_from_text(text))
    
    # Extract from attributes if available
    if attributes&.dig('attributes')
      attributes['attributes'].each do |attr|
        keywords.concat(extract_keywords_from_text(attr['tag_name'] || ''))
        keywords.concat(extract_keywords_from_text(attr['description'] || ''))
      end
    end
    
    keywords.uniq
  end
  
  def extract_product_keywords(product_name, product_description)
    text = [product_name, product_description].compact.join(' ')
    extract_keywords_from_text(text)
  end
  
  def extract_keywords_from_text(text)
    return [] if text.blank?
    
    # Remove common words and extract meaningful terms
    stop_words = %w[и в на с для по из к от до при про под над между через]
    
    words = text.downcase.split(/\s+/).map(&:strip).reject(&:blank?)
    words = words.reject { |word| stop_words.include?(word) }
    words = words.select { |word| word.length > 2 }
    
    words.uniq
  end
end
