# frozen_string_literal: true

class Avito
  #0jyGhZMf4W5W-gXgbGi-
  # sec Dd0HYx0cl46Zw4RfzZMoYGcM6UROzEu374OzoT9F


  # Мебель и интерьер - Шк<PERSON><PERSON><PERSON>, комоды и стеллажи


  # Товары для животных
  # Продукты питания
  # Посуда и товары для кухни - Посуда, Товары для кухни
  # Красота и здоровье - —  # Косметика  # —  # Парфюмерия   # —  # Приборы и аксессуары  # —  # Средства гигиены  # —  # Средства для волос # —  # Медицинские изделия
  # Оргтехника и расходники - Б<PERSON><PERSON>ага, Кабели и адаптеры, Картриджи, Канцелярия

  def get_avito_category(sp_cat)
    c = AvitoCategory.where(sp_cat: sp_cat).first
    return nil unless c

    t = c.path.split('~')
    ret = {}
    ret['Category'] = t[0]
    ret['GoodsType'] = t[1] if t[1]
    ret['GoodsSubType'] = t[2] if t[2]
    ret[c.last_attr_name] = t[3] if t[3]

    {cat: ret, condition: c.condition}
  end

  def get_total_stock(sizes)
    s = sizes.split(',')
    total = 0
    s.each do |size|
      total += size.split('@')[1].to_i
    end
    total
  end

  def get_stock_text(sizes)
    s = sizes.split(',').map {|t| t.split('@')}
    s.delete_if {|t| t[1].to_i<=0}
    if s.length == 1
      if s[0][0] == '-' || s[0][0].downcase.start_with?('цена')
        return "#{s[0][1]} шт."
      else
        return "#{s[0][0]} - #{s[0][1]} шт."
      end
    else
      s.map {|t| "#{t[0]} - #{t[1]} шт."}.join(', ')
    end

  end

  def load_avito_data

  end
  def fill_avito_data(prod)

  end
  def product_to_xml(prod)

    return nil if prod.pictures.nil? || prod.pictures.empty?

    return nil if prod[:sizes].nil? || prod[:sizes].empty?

    stock = get_total_stock(prod[:sizes])
    return nil if stock == 0

    ad = Ox::Element.new('Ad')

    id = Ox::Element.new('Id')
    id << prod[:gid].to_s
    ad << id

    address = Ox::Element.new('Address')
    address << 'Владивосток, Калинина, 160'
    ad << address

    fill_avite_data(prod) unless prod.avito_data
    avito_data = prod.avito_data
    #get_avito_category(prod[:sp_category_id])
    return nil unless avito_data

    avito_data[:cat].each do |k, v|
      cat = Ox::Element.new(k)
      cat << v
      ad << cat
    end

    ad_type = Ox::Element.new('AdType')
    ad_type << 'Товар приобретен на продажу'
    ad << ad_type

    condition = Ox::Element.new('Condition')
    condition << avito_data[:condition]
    ad << condition

    price = Ox::Element.new('Price')
    p = (prod[:price].to_f * (1.0 + prod[:sp_fee].to_f/100) * 1.03).ceil
    price << p.to_s
    ad << price

    title = Ox::Element.new('Title')
    title << prod[:name].to_s
    ad << title


    desc = Ox::Element.new('Description')
    text = prod[:description]

    stock = get_stock_text(prod.sizes)
    if stock
      text += "\n" unless text.end_with?("\n")
      text += "Наличие: " + stock
    end

    text.strip!

    desc << Ox::CData.new(text)

    ad << desc

    contact = Ox::Element.new('Contact')

    images = Ox::Element.new('Images')
    prod.pictures[0..9].each do |img|
      image = Ox::Element.new('Image')
      image[:url] = img
      images << image
    end
    ad << images

    ad
  end

  def get_sp_category_list
    SpProduct.joins(:sp_purchase).where('gid > 0').where(disabled:false).where('price>=800').where('sp_purchases.delivery_days > 0 AND sp_purchases.delivery_days < 5').group(:sp_category_id).order('count_all DESC').count
  end
  def get_product_list
    SpProduct.joins(:sp_purchase).where('gid > 0').where(disabled:false).where('price>=1000').where('sp_purchases.delivery_days > 0 AND sp_purchases.delivery_days < 5')
  end

  def parse_category_xlsx(file)

  end
  def create_file
    doc = Ox::Document.new

    top = Ox::Element.new('Ads')
    top[:formatVersion] = '3'
    top[:target] = 'Avito.ru'


    #SpProduct.where('gid > 0').where('price>500').each do |p|
    products = get_product_list

    products.each do |p|
      next if p.sp_category_id == 0
      next if p.price == 0

      ad = product_to_xml(p)
      next unless ad
      top << ad
    end
    doc << top
    Ox.to_file('/home/<USER>/avito.xml', doc)
  end
end
