# frozen_string_literal: true

require 'singleton'
require 'json'

class AvitoDataLoader
  include Singleton
  
  attr_reader :categories, :category_attributes, :loaded_at
  
  AVITO_DATA_PATH = 'G:/work/avito2/avito_data'
  CATEGORIES_FILE = File.join(AVITO_DATA_PATH, 'categories.json')
  ATTRIBUTES_DIR = File.join(AVITO_DATA_PATH, 'attributes')
  
  def initialize
    @categories = {}
    @category_attributes = {}
    @categories_by_name = {}
    @categories_by_path = {}
    @loaded_at = nil
  end
  
  # Load all Avito category data into memory
  def load_all_data
    Rails.logger.info "Loading Avito category data from #{AVITO_DATA_PATH}"
    
    load_categories
    load_category_attributes
    build_search_indexes
    
    @loaded_at = Time.current
    Rails.logger.info "Avito data loaded successfully. Categories: #{@categories.count}, Attributes: #{@category_attributes.count}"
    
    true
  rescue => e
    Rails.logger.error "Failed to load Avito data: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    false
  end
  
  # Load main categories from categories.json
  def load_categories
    unless File.exist?(CATEGORIES_FILE)
      raise "Categories file not found: #{CATEGORIES_FILE}"
    end
    
    categories_data = JSON.parse(File.read(CATEGORIES_FILE))
    
    categories_data.each do |category|
      id = category['id']
      @categories[id] = {
        id: id,
        name: category['name'],
        full_path: category['full_path'],
        url: category['url'],
        path_parts: category['full_path'].split(' / ')
      }
    end
    
    Rails.logger.info "Loaded #{@categories.count} categories"
  end
  
  # Load detailed attributes for each category
  def load_category_attributes
    unless Dir.exist?(ATTRIBUTES_DIR)
      raise "Attributes directory not found: #{ATTRIBUTES_DIR}"
    end
    
    Dir.glob(File.join(ATTRIBUTES_DIR, 'category_*.json')).each do |file|
      category_id = File.basename(file, '.json').gsub('category_', '')
      
      begin
        attribute_data = JSON.parse(File.read(file))
        @category_attributes[category_id] = attribute_data
      rescue JSON::ParserError => e
        Rails.logger.warn "Failed to parse attributes file #{file}: #{e.message}"
      end
    end
    
    Rails.logger.info "Loaded attributes for #{@category_attributes.count} categories"
  end
  
  # Build search indexes for efficient lookups
  def build_search_indexes
    @categories_by_name = {}
    @categories_by_path = {}
    
    @categories.each do |id, category|
      # Index by name (case-insensitive)
      name_key = category[:name].downcase
      @categories_by_name[name_key] ||= []
      @categories_by_name[name_key] << id
      
      # Index by full path (case-insensitive)
      path_key = category[:full_path].downcase
      @categories_by_path[path_key] = id
      
      # Index by path parts for partial matching
      category[:path_parts].each do |part|
        part_key = part.strip.downcase
        @categories_by_name[part_key] ||= []
        @categories_by_name[part_key] << id unless @categories_by_name[part_key].include?(id)
      end
    end
  end
  
  # Get category by ID
  def get_category(id)
    @categories[id.to_s]
  end
  
  # Get category attributes by ID
  def get_category_attributes(id)
    @category_attributes[id.to_s]
  end
  
  # Search categories by name (fuzzy matching)
  def search_categories_by_name(query, limit: 10)
    return [] if query.blank?
    
    query_lower = query.downcase
    results = []
    
    # Exact matches first
    if @categories_by_name[query_lower]
      @categories_by_name[query_lower].each do |id|
        results << @categories[id]
      end
    end
    
    # Partial matches
    @categories_by_name.each do |name, ids|
      next if results.size >= limit
      
      if name.include?(query_lower) && !@categories_by_name[query_lower]&.include?(ids.first)
        ids.each do |id|
          results << @categories[id]
          break if results.size >= limit
        end
      end
    end
    
    results.uniq.first(limit)
  end
  
  # Search categories by path
  def search_categories_by_path(path_query, limit: 10)
    return [] if path_query.blank?
    
    path_lower = path_query.downcase
    results = []
    
    @categories_by_path.each do |path, id|
      break if results.size >= limit
      
      if path.include?(path_lower)
        results << @categories[id]
      end
    end
    
    results.first(limit)
  end
  
  # Get all categories for a specific top-level category
  def get_categories_by_root(root_category)
    return [] if root_category.blank?
    
    root_lower = root_category.downcase
    results = []
    
    @categories.each do |id, category|
      if category[:path_parts].first&.downcase == root_lower
        results << category
      end
    end
    
    results
  end
  
  # Get category suggestions for a product based on name and description
  def suggest_categories_for_product(product_name, product_description = nil, limit: 5)
    suggestions = []
    
    # Extract keywords from product name and description
    text = [product_name, product_description].compact.join(' ').downcase
    keywords = extract_keywords(text)
    
    # Score categories based on keyword matches
    category_scores = {}
    
    @categories.each do |id, category|
      score = calculate_category_score(category, keywords)
      category_scores[id] = score if score > 0
    end
    
    # Sort by score and return top suggestions
    top_categories = category_scores.sort_by { |_, score| -score }.first(limit)
    
    top_categories.map do |id, score|
      {
        category: @categories[id],
        attributes: @category_attributes[id],
        score: score
      }
    end
  end
  
  # Check if data needs to be reloaded
  def data_stale?
    return true if @loaded_at.nil?
    
    # Check if files have been modified since last load
    categories_mtime = File.mtime(CATEGORIES_FILE) if File.exist?(CATEGORIES_FILE)
    return true if categories_mtime && categories_mtime > @loaded_at
    
    # Check attributes directory
    if Dir.exist?(ATTRIBUTES_DIR)
      Dir.glob(File.join(ATTRIBUTES_DIR, '*.json')).each do |file|
        return true if File.mtime(file) > @loaded_at
      end
    end
    
    false
  end
  
  # Reload data if stale
  def ensure_fresh_data
    load_all_data if data_stale?
  end
  
  # Get statistics about loaded data
  def stats
    {
      categories_count: @categories.count,
      attributes_count: @category_attributes.count,
      loaded_at: @loaded_at,
      data_stale: data_stale?
    }
  end
  
  private
  
  # Extract meaningful keywords from text
  def extract_keywords(text)
    # Remove common words and extract meaningful terms
    stop_words = %w[и в на с для по из к от до при про под над между через]
    
    words = text.split(/\s+/).map(&:strip).reject(&:blank?)
    words = words.reject { |word| stop_words.include?(word) }
    words = words.select { |word| word.length > 2 }
    
    words.uniq
  end
  
  # Calculate relevance score for a category based on keywords
  def calculate_category_score(category, keywords)
    score = 0
    
    # Check category name
    category_text = [category[:name], category[:full_path]].join(' ').downcase
    
    keywords.each do |keyword|
      if category_text.include?(keyword)
        # Exact match in name gets higher score
        if category[:name].downcase.include?(keyword)
          score += 10
        else
          score += 5
        end
      end
    end
    
    score
  end
end
