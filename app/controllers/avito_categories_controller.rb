class AvitoCategoriesController < ApplicationController
  before_action :ensure_data_loaded
  
  def index
    @stats = AvitoDataLoader.instance.stats
    @recent_assignments = get_recent_assignments
  end
  
  def search
    @query = params[:query]
    @results = []
    
    if @query.present?
      @results = AvitoDataLoader.instance.search_categories_by_name(@query, limit: 20)
    end
    
    respond_to do |format|
      format.html
      format.json { render json: @results }
    end
  end
  
  def show
    @category_id = params[:id]
    @category = AvitoDataLoader.instance.get_category(@category_id)
    @attributes = AvitoDataLoader.instance.get_category_attributes(@category_id)
    
    if @category.nil?
      redirect_to avito_categories_path, alert: "Category not found"
      return
    end
    
    @required_attributes = AvitoCategoryMatcher.new.get_required_attributes(@category_id)
  end
  
  def test_match
    @product_name = params[:product_name]
    @product_description = params[:product_description]
    @result = nil
    
    if @product_name.present?
      matcher = AvitoCategoryMatcher.new
      @result = matcher.find_best_category(
        @product_name, 
        @product_description, 
        use_llm: false
      )
      
      if @result
        @validation = matcher.validate_category_assignment(
          @result[:category][:id],
          @product_name,
          @product_description
        )
      end
    end
    
    respond_to do |format|
      format.html
      format.json { render json: { result: @result, validation: @validation } }
    end
  end
  
  def batch_categorize
    @limit = params[:limit]&.to_i || 10
    @results = []
    @report = nil
    
    if request.post?
      # Get sample products
      products = SpProduct.joins(:sp_purchase)
                         .where('gid > 0')
                         .where(disabled: false)
                         .where('price >= 500')
                         .limit(@limit)
                         .pluck(:id, :name, :description)
                         .map { |id, name, desc| { id: id, name: name, description: desc } }
      
      if products.any?
        matcher = AvitoCategoryMatcher.new
        @results = matcher.batch_categorize(products, use_llm: false, delay: 0)
        @report = matcher.generate_assignment_report(@results)
      end
    end
    
    respond_to do |format|
      format.html
      format.json { render json: { results: @results, report: @report } }
    end
  end
  
  def reload_data
    success = AvitoDataLoader.instance.load_all_data
    
    if success
      redirect_to avito_categories_path, notice: "Avito data reloaded successfully"
    else
      redirect_to avito_categories_path, alert: "Failed to reload Avito data"
    end
  end
  
  def export
    require 'csv'
    
    loader = AvitoDataLoader.instance
    
    csv_data = CSV.generate do |csv|
      csv << ['ID', 'Name', 'Full Path', 'URL', 'Has Attributes']
      
      loader.categories.each do |id, category|
        has_attributes = loader.get_category_attributes(id) ? 'Yes' : 'No'
        csv << [
          category[:id],
          category[:name],
          category[:full_path],
          category[:url],
          has_attributes
        ]
      end
    end
    
    filename = "avito_categories_#{Time.current.strftime('%Y%m%d_%H%M%S')}.csv"
    
    respond_to do |format|
      format.csv do
        send_data csv_data, 
                  filename: filename,
                  type: 'text/csv',
                  disposition: 'attachment'
      end
    end
  end
  
  private
  
  def ensure_data_loaded
    AvitoDataLoader.instance.ensure_fresh_data
  end
  
  def get_recent_assignments
    # Get recent products that have been assigned Avito categories
    SpProduct.where.not(avito_data: nil)
             .order(updated_at: :desc)
             .limit(10)
             .pluck(:id, :name, :avito_data)
             .map do |id, name, avito_data|
               {
                 id: id,
                 name: name,
                 category_name: avito_data&.dig('category_name'),
                 full_path: avito_data&.dig('full_path'),
                 match_score: avito_data&.dig('match_score')
               }
             end
  rescue
    []
  end
end
