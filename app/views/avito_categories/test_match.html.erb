<% content_for :title, "Test Category Matching" %>

<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <h1>Test Category Matching</h1>
      
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item"><%= link_to "Avito Categories", avito_categories_path %></li>
          <li class="breadcrumb-item active">Test Match</li>
        </ol>
      </nav>
      
      <div class="card mb-4">
        <div class="card-header">
          <h5>Product Information</h5>
        </div>
        <div class="card-body">
          <%= form_with url: test_match_avito_categories_path, method: :get, local: true do |f| %>
            <div class="form-group">
              <%= f.label :product_name, "Product Name *" %>
              <%= f.text_field :product_name, value: @product_name, 
                  placeholder: "Enter product name...", 
                  class: "form-control", required: true %>
            </div>
            
            <div class="form-group">
              <%= f.label :product_description, "Product Description (optional)" %>
              <%= f.text_area :product_description, value: @product_description, 
                  placeholder: "Enter product description for better matching...", 
                  class: "form-control", rows: 3 %>
            </div>
            
            <div class="form-group">
              <%= f.submit "Find Best Category", class: "btn btn-primary" %>
              <%= link_to "Clear", test_match_avito_categories_path, class: "btn btn-secondary" %>
            </div>
          <% end %>
        </div>
      </div>
      
      <% if @result %>
        <div class="card mb-4">
          <div class="card-header bg-success text-white">
            <h5>✅ Best Category Match Found</h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-8">
                <h4><%= @result[:category][:name] %></h4>
                <p class="text-muted"><%= @result[:category][:full_path] %></p>
                
                <div class="mt-3">
                  <strong>Category ID:</strong> <code><%= @result[:category][:id] %></code><br>
                  <strong>Match Score:</strong> 
                  <span class="badge badge-<%= @result[:score] > 20 ? 'success' : @result[:score] > 10 ? 'warning' : 'secondary' %>">
                    <%= @result[:score] %>
                  </span><br>
                  <strong>URL:</strong> 
                  <a href="<%= @result[:category][:url] %>" target="_blank" class="btn btn-sm btn-outline-primary">
                    View on Avito
                  </a>
                </div>
              </div>
              
              <div class="col-md-4">
                <div class="text-right">
                  <%= link_to "View Category Details", avito_category_path(@result[:category][:id]), 
                      class: "btn btn-info" %>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <% if @validation %>
          <div class="card mb-4">
            <div class="card-header">
              <h5>Validation Results</h5>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <strong>Valid Assignment:</strong>
                  <span class="badge badge-<%= @validation[:valid] ? 'success' : 'danger' %>">
                    <%= @validation[:valid] ? 'Yes' : 'No' %>
                  </span>
                </div>
                
                <div class="col-md-6">
                  <strong>Confidence:</strong>
                  <span class="badge badge-<%= @validation[:confidence] > 0.5 ? 'success' : @validation[:confidence] > 0.2 ? 'warning' : 'secondary' %>">
                    <%= (@validation[:confidence] * 100).round(1) %>%
                  </span>
                </div>
              </div>
              
              <% if @validation[:matching_keywords]&.any? %>
                <div class="mt-3">
                  <strong>Matching Keywords:</strong>
                  <% @validation[:matching_keywords].each do |keyword| %>
                    <span class="badge badge-light"><%= keyword %></span>
                  <% end %>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>
        
        <% if @result[:attributes] %>
          <div class="card">
            <div class="card-header">
              <h5>Required Attributes for this Category</h5>
            </div>
            <div class="card-body">
              <% required_attrs = AvitoCategoryMatcher.new.get_required_attributes(@result[:category][:id]) %>
              
              <% if required_attrs.any? %>
                <div class="table-responsive">
                  <table class="table table-sm">
                    <thead>
                      <tr>
                        <th>Attribute</th>
                        <th>Type</th>
                        <th>Description</th>
                        <th>Example</th>
                      </tr>
                    </thead>
                    <tbody>
                      <% required_attrs.each do |attr| %>
                        <tr>
                          <td><strong><%= attr[:tag_name] %></strong></td>
                          <td><code><%= attr[:value_type] %></code></td>
                          <td><%= truncate(attr[:description], length: 100) %></td>
                          <td>
                            <% if attr[:example] %>
                              <code><%= attr[:example] %></code>
                            <% end %>
                          </td>
                        </tr>
                      <% end %>
                    </tbody>
                  </table>
                </div>
              <% else %>
                <div class="alert alert-info">
                  No required attributes found for this category.
                </div>
              <% end %>
            </div>
          </div>
        <% end %>
        
      <% elsif @product_name.present? %>
        <div class="card">
          <div class="card-header bg-warning text-dark">
            <h5>❌ No Category Match Found</h5>
          </div>
          <div class="card-body">
            <p>No suitable Avito category could be found for the product "<%= @product_name %>".</p>
            
            <h6>Suggestions:</h6>
            <ul>
              <li>Try adding more descriptive keywords to the product name</li>
              <li>Add a detailed product description</li>
              <li>Check if the product fits into Avito's allowed categories</li>
              <li>Search manually using the <%= link_to "category search", search_avito_categories_path %></li>
            </ul>
          </div>
        </div>
      <% else %>
        <div class="alert alert-info">
          <h5>Enter product information above</h5>
          <p>Provide at least a product name to test the automatic category matching system.</p>
          
          <h6>How it works:</h6>
          <ul>
            <li>The system analyzes your product name and description</li>
            <li>It searches through <%= number_with_delimiter(AvitoDataLoader.instance.stats[:categories_count]) %> Avito categories</li>
            <li>Returns the best matching category with a confidence score</li>
            <li>Shows required attributes for the matched category</li>
          </ul>
        </div>
      <% end %>
    </div>
  </div>
</div>
