<% content_for :title, "Avito Categories" %>

<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <h1>Avito Category Management</h1>
      
      <div class="row mb-4">
        <div class="col-md-3">
          <div class="card">
            <div class="card-body">
              <h5 class="card-title">Categories Loaded</h5>
              <h2 class="text-primary"><%= @stats[:categories_count] %></h2>
            </div>
          </div>
        </div>
        
        <div class="col-md-3">
          <div class="card">
            <div class="card-body">
              <h5 class="card-title">Attributes Loaded</h5>
              <h2 class="text-success"><%= @stats[:attributes_count] %></h2>
            </div>
          </div>
        </div>
        
        <div class="col-md-3">
          <div class="card">
            <div class="card-body">
              <h5 class="card-title">Last Loaded</h5>
              <p class="card-text">
                <%= @stats[:loaded_at] ? time_ago_in_words(@stats[:loaded_at]) + " ago" : "Never" %>
              </p>
            </div>
          </div>
        </div>
        
        <div class="col-md-3">
          <div class="card">
            <div class="card-body">
              <h5 class="card-title">Data Status</h5>
              <span class="badge <%= @stats[:data_stale] ? 'badge-warning' : 'badge-success' %>">
                <%= @stats[:data_stale] ? 'Stale' : 'Fresh' %>
              </span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="row mb-4">
        <div class="col-12">
          <div class="btn-group" role="group">
            <%= link_to "Search Categories", search_avito_categories_path, class: "btn btn-primary" %>
            <%= link_to "Test Category Matching", test_match_avito_categories_path, class: "btn btn-info" %>
            <%= link_to "Batch Categorize", batch_categorize_avito_categories_path, class: "btn btn-warning" %>
            <%= link_to "Reload Data", reload_data_avito_categories_path, method: :post, class: "btn btn-secondary", 
                confirm: "This will reload all category data. Continue?" %>
            <%= link_to "Export CSV", export_avito_categories_path(format: :csv), class: "btn btn-success" %>
          </div>
        </div>
      </div>
      
      <% if @recent_assignments.any? %>
        <div class="card">
          <div class="card-header">
            <h5>Recent Category Assignments</h5>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-sm">
                <thead>
                  <tr>
                    <th>Product ID</th>
                    <th>Product Name</th>
                    <th>Assigned Category</th>
                    <th>Full Path</th>
                    <th>Match Score</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <% @recent_assignments.each do |assignment| %>
                    <tr>
                      <td><%= assignment[:id] %></td>
                      <td><%= truncate(assignment[:name], length: 50) %></td>
                      <td><%= assignment[:category_name] %></td>
                      <td><%= truncate(assignment[:full_path], length: 60) %></td>
                      <td>
                        <% if assignment[:match_score] %>
                          <span class="badge badge-<%= assignment[:match_score] > 20 ? 'success' : assignment[:match_score] > 10 ? 'warning' : 'secondary' %>">
                            <%= assignment[:match_score] %>
                          </span>
                        <% end %>
                      </td>
                      <td>
                        <%= link_to "View Product", sp_product_path(assignment[:id]), class: "btn btn-sm btn-outline-primary" if defined?(sp_product_path) %>
                      </td>
                    </tr>
                  <% end %>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      <% end %>
      
      <div class="mt-4">
        <h3>Quick Actions</h3>
        <div class="row">
          <div class="col-md-6">
            <div class="card">
              <div class="card-body">
                <h5 class="card-title">Search Categories</h5>
                <p class="card-text">Find Avito categories by name or keywords</p>
                <%= form_with url: search_avito_categories_path, method: :get, local: true, class: "form-inline" do |f| %>
                  <%= f.text_field :query, placeholder: "Enter search term...", class: "form-control mr-2" %>
                  <%= f.submit "Search", class: "btn btn-primary" %>
                <% end %>
              </div>
            </div>
          </div>
          
          <div class="col-md-6">
            <div class="card">
              <div class="card-body">
                <h5 class="card-title">Test Product Matching</h5>
                <p class="card-text">Test category matching for a specific product</p>
                <%= form_with url: test_match_avito_categories_path, method: :get, local: true do |f| %>
                  <%= f.text_field :product_name, placeholder: "Enter product name...", class: "form-control mb-2" %>
                  <%= f.submit "Find Category", class: "btn btn-info" %>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
