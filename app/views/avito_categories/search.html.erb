<% content_for :title, "Search Avito Categories" %>

<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <h1>Search Avito Categories</h1>
      
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item"><%= link_to "Avito Categories", avito_categories_path %></li>
          <li class="breadcrumb-item active">Search</li>
        </ol>
      </nav>
      
      <div class="card mb-4">
        <div class="card-body">
          <%= form_with url: search_avito_categories_path, method: :get, local: true do |f| %>
            <div class="form-group">
              <%= f.label :query, "Search Term" %>
              <%= f.text_field :query, value: @query, placeholder: "Enter category name or keywords...", 
                  class: "form-control", autofocus: true %>
              <small class="form-text text-muted">
                Search by category name, keywords, or path components
              </small>
            </div>
            
            <div class="form-group">
              <%= f.submit "Search", class: "btn btn-primary" %>
              <%= link_to "Clear", search_avito_categories_path, class: "btn btn-secondary" %>
            </div>
          <% end %>
        </div>
      </div>
      
      <% if @query.present? %>
        <div class="card">
          <div class="card-header">
            <h5>Search Results for "<%= @query %>"</h5>
            <small class="text-muted"><%= @results.count %> categories found</small>
          </div>
          
          <div class="card-body">
            <% if @results.any? %>
              <div class="table-responsive">
                <table class="table table-hover">
                  <thead>
                    <tr>
                      <th>Category Name</th>
                      <th>Full Path</th>
                      <th>Category ID</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    <% @results.each do |category| %>
                      <tr>
                        <td>
                          <strong><%= category[:name] %></strong>
                        </td>
                        <td>
                          <small class="text-muted"><%= category[:full_path] %></small>
                        </td>
                        <td>
                          <code><%= category[:id] %></code>
                        </td>
                        <td>
                          <%= link_to "View Details", avito_category_path(category[:id]), 
                              class: "btn btn-sm btn-outline-primary" %>
                          
                          <button type="button" class="btn btn-sm btn-outline-info" 
                                  onclick="testCategoryMatch('<%= category[:id] %>', '<%= j(category[:name]) %>')">
                            Test Match
                          </button>
                        </td>
                      </tr>
                    <% end %>
                  </tbody>
                </table>
              </div>
            <% else %>
              <div class="alert alert-info">
                <h5>No categories found</h5>
                <p>Try different search terms or check the spelling.</p>
                
                <h6>Search Tips:</h6>
                <ul>
                  <li>Use specific keywords related to the product category</li>
                  <li>Try broader terms if specific searches don't work</li>
                  <li>Search in Russian as categories are in Russian</li>
                </ul>
              </div>
            <% end %>
          </div>
        </div>
      <% else %>
        <div class="alert alert-info">
          <h5>Enter a search term above</h5>
          <p>Search through <%= number_with_delimiter(AvitoDataLoader.instance.stats[:categories_count]) %> 
             available Avito categories.</p>
          
          <h6>Popular categories include:</h6>
          <ul>
            <li>Одежда, обувь, аксессуары</li>
            <li>Товары для дома и дачи</li>
            <li>Электроника</li>
            <li>Красота и здоровье</li>
            <li>Спорт и отдых</li>
          </ul>
        </div>
      <% end %>
    </div>
  </div>
</div>

<script>
function testCategoryMatch(categoryId, categoryName) {
  const productName = prompt(`Test category "${categoryName}" with product name:`);
  if (productName) {
    const url = '<%= test_match_avito_categories_path %>';
    const form = document.createElement('form');
    form.method = 'GET';
    form.action = url;
    
    const productInput = document.createElement('input');
    productInput.type = 'hidden';
    productInput.name = 'product_name';
    productInput.value = productName;
    form.appendChild(productInput);
    
    document.body.appendChild(form);
    form.submit();
  }
}
</script>
