# Avito Categories System

This system loads all Avito category data into memory and provides automatic category matching for products.

## Overview

The system consists of several components:

1. **AvitoDataLoader** - Singleton service that loads and manages category data
2. **AvitoCategoryMatcher** - Service for finding best category matches for products
3. **AvitoCategoriesController** - Web interface for testing and managing categories
4. **Rake tasks** - Command-line tools for data management

## Data Structure

The system expects data in the following location:
- **Categories file**: `G:/work/avito2/avito_data/categories.json`
- **Attributes folder**: `G:/work/avito2/avito_data/attributes/`

### Categories.json format:
```json
[
  {
    "id": "98189",
    "name": "Краны и балки",
    "full_path": "Готовый бизнес и оборудование / Оборудование для бизнеса / Логистика и склад / Грузоподъёмное / Краны и балки",
    "url": "https://www.avito.ru/autoload/documentation/templates/98189"
  }
]
```

### Attributes files format (category_116666.json):
```json
{
  "id": "116666",
  "name": "Автозагрузка",
  "full_path": "Для дома и дачи / Ремонт и строительство / Для сада и дачи / Садовая техника / Воздуходувки и пылесосы",
  "attributes": [
    {
      "group": "Общие элементы",
      "tag_name": "Уникальный идентификатор объявления",
      "conditions": ["Обязательный"],
      "description": "...",
      "value_type": "free_text",
      "values": null,
      "example": "xjfdge4735202"
    }
  ]
}
```

## Usage

### Loading Data

#### Via Rake Task:
```bash
# Load all category data
rake avito:load_data

# Check data statistics
rake avito:stats

# Search categories
rake avito:search['краны']

# Test category matching
rake avito:test_match['Кран башенный']

# Batch categorize products
rake avito:batch_categorize[50]

# Show category details
rake avito:category_info[98189]

# Export categories to CSV
rake avito:export_mappings
```

#### Via Ruby Code:
```ruby
# Load data
loader = AvitoDataLoader.instance
loader.load_all_data

# Search categories
results = loader.search_categories_by_name('краны')

# Get category details
category = loader.get_category('98189')
attributes = loader.get_category_attributes('98189')

# Find best category for a product
matcher = AvitoCategoryMatcher.new
result = matcher.find_best_category('Кран башенный', 'Описание крана')
```

### Web Interface

Visit `/avito_categories` to access the web interface:

- **Dashboard** - View statistics and recent assignments
- **Search** - Search through categories by name or keywords
- **Test Match** - Test category matching for specific products
- **Batch Categorize** - Process multiple products at once

### Automatic Category Assignment

The system automatically assigns categories to products when `fill_avito_data` is called:

```ruby
# This will automatically find and assign the best category
avito = Avito.new
avito.fill_avito_data(product)

# The product will now have avito_data populated with:
# - Category hierarchy (Category, GoodsType, GoodsSubType)
# - Category ID and name
# - Match score
# - Full category path
```

## API Methods

### AvitoDataLoader

- `load_all_data` - Load categories and attributes from files
- `get_category(id)` - Get category by ID
- `get_category_attributes(id)` - Get attributes for category
- `search_categories_by_name(query)` - Search categories by name
- `search_categories_by_path(path)` - Search by full path
- `suggest_categories_for_product(name, desc)` - Get suggestions for product
- `stats` - Get loading statistics
- `ensure_fresh_data` - Reload if data is stale

### AvitoCategoryMatcher

- `find_best_category(name, desc, options)` - Find best matching category
- `batch_categorize(products, options)` - Process multiple products
- `validate_category_assignment(id, name, desc)` - Validate assignment
- `get_required_attributes(category_id)` - Get required attributes
- `generate_assignment_report(assignments)` - Generate statistics

## Configuration

### File Paths
Update paths in `app/services/avito_data_loader.rb`:
```ruby
AVITO_DATA_PATH = 'G:/work/avito2/avito_data'
CATEGORIES_FILE = File.join(AVITO_DATA_PATH, 'categories.json')
ATTRIBUTES_DIR = File.join(AVITO_DATA_PATH, 'attributes')
```

### Matching Algorithm
The system uses keyword-based matching with scoring:
- Exact name matches: 10 points
- Path component matches: 5 points
- Categories are ranked by total score

## Integration with LLM

The system is designed to integrate with LLM services for improved categorization. To implement:

1. Update `ask_llm_for_category` method in `AvitoCategoryMatcher`
2. Add your LLM API integration
3. Set `use_llm: true` when calling matching methods

Example LLM integration:
```ruby
def ask_llm_for_category(product_name, product_description, suggestions)
  # Format suggestions for LLM
  categories_text = suggestions.map do |s|
    "#{s[:category][:id]}: #{s[:category][:name]} (#{s[:category][:full_path]})"
  end.join("\n")
  
  prompt = "Product: #{product_name}\nDescription: #{product_description}\n\nAvailable categories:\n#{categories_text}\n\nBest category ID:"
  
  # Call your LLM service
  response = YourLLMService.call(prompt)
  
  # Find matching suggestion
  suggestions.find { |s| s[:category][:id] == response.strip }
end
```

## Performance

- Data is loaded once and cached in memory
- Automatic reload when files are modified
- Efficient search indexes for fast lookups
- Batch processing support for large datasets

## Troubleshooting

### Data Not Loading
1. Check file paths are correct
2. Verify JSON format is valid
3. Check file permissions
4. Review Rails logs for errors

### Poor Matching Results
1. Add more descriptive product names
2. Include detailed descriptions
3. Implement LLM integration
4. Manually map common categories

### Memory Usage
- The system loads all data into memory
- Monitor memory usage with large category sets
- Consider pagination for very large datasets

## Examples

### Basic Usage
```ruby
# Load data
AvitoDataLoader.instance.load_all_data

# Find category for a product
matcher = AvitoCategoryMatcher.new
result = matcher.find_best_category("Женская куртка", "Зимняя куртка размер M")

if result
  puts "Category: #{result[:category][:name]}"
  puts "Path: #{result[:category][:full_path]}"
  puts "Score: #{result[:score]}"
end
```

### Batch Processing
```ruby
products = [
  { name: "Женская куртка", description: "Зимняя куртка" },
  { name: "Мужские ботинки", description: "Кожаные ботинки" }
]

matcher = AvitoCategoryMatcher.new
results = matcher.batch_categorize(products)

results.each do |result|
  product = result[:product]
  match = result[:category_match]
  
  if match
    puts "#{product[:name]} -> #{match[:category][:name]}"
  else
    puts "#{product[:name]} -> No category found"
  end
end
```
