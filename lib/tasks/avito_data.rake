namespace :avito do
  desc "Load all Avito category data into memory"
  task load_data: :environment do
    puts "Loading Avito category data..."
    
    loader = AvitoDataLoader.instance
    
    if loader.load_all_data
      stats = loader.stats
      puts "✅ Successfully loaded Avito data:"
      puts "   Categories: #{stats[:categories_count]}"
      puts "   Attributes: #{stats[:attributes_count]}"
      puts "   Loaded at: #{stats[:loaded_at]}"
    else
      puts "❌ Failed to load Avito data"
      exit 1
    end
  end
  
  desc "Show Avito data statistics"
  task stats: :environment do
    loader = AvitoDataLoader.instance
    stats = loader.stats
    
    puts "Avito Data Statistics:"
    puts "====================="
    puts "Categories loaded: #{stats[:categories_count]}"
    puts "Attributes loaded: #{stats[:attributes_count]}"
    puts "Last loaded: #{stats[:loaded_at] || 'Never'}"
    puts "Data stale: #{stats[:data_stale] ? 'Yes' : 'No'}"
    
    if stats[:categories_count] > 0
      puts "\nSample categories:"
      loader.categories.first(5).each do |id, category|
        puts "  #{id}: #{category[:name]}"
      end
    end
  end
  
  desc "Search categories by name"
  task :search, [:query] => :environment do |t, args|
    query = args[:query]
    
    if query.blank?
      puts "Usage: rake avito:search['search term']"
      exit 1
    end
    
    loader = AvitoDataLoader.instance
    loader.ensure_fresh_data
    
    results = loader.search_categories_by_name(query, limit: 10)
    
    puts "Search results for '#{query}':"
    puts "=============================="
    
    if results.empty?
      puts "No categories found"
    else
      results.each_with_index do |category, index|
        puts "#{index + 1}. #{category[:name]}"
        puts "   Path: #{category[:full_path]}"
        puts "   ID: #{category[:id]}"
        puts
      end
    end
  end
  
  desc "Test category matching for a product"
  task :test_match, [:product_name] => :environment do |t, args|
    product_name = args[:product_name]
    
    if product_name.blank?
      puts "Usage: rake avito:test_match['Product Name']"
      exit 1
    end
    
    matcher = AvitoCategoryMatcher.new
    
    puts "Finding category for product: #{product_name}"
    puts "============================================="
    
    result = matcher.find_best_category(product_name, nil, use_llm: false)
    
    if result
      puts "Best match:"
      puts "  Category: #{result[:category][:name]}"
      puts "  Path: #{result[:category][:full_path]}"
      puts "  Score: #{result[:score]}"
      puts "  ID: #{result[:category][:id]}"
      
      if result[:attributes]
        required_attrs = matcher.get_required_attributes(result[:category][:id])
        if required_attrs.any?
          puts "\nRequired attributes:"
          required_attrs.each do |attr|
            puts "  - #{attr[:tag_name]}"
          end
        end
      end
    else
      puts "No matching category found"
    end
  end
  
  desc "Batch categorize products from SP"
  task :batch_categorize, [:limit] => :environment do |t, args|
    limit = args[:limit]&.to_i || 10
    
    puts "Batch categorizing #{limit} products..."
    
    # Get sample products
    products = SpProduct.joins(:sp_purchase)
                       .where('gid > 0')
                       .where(disabled: false)
                       .where('price >= 500')
                       .limit(limit)
                       .pluck(:id, :name, :description)
                       .map { |id, name, desc| { id: id, name: name, description: desc } }
    
    if products.empty?
      puts "No products found to categorize"
      exit 0
    end
    
    matcher = AvitoCategoryMatcher.new
    
    results = matcher.batch_categorize(products, use_llm: false, delay: 0.1)
    
    puts "\nCategorization Results:"
    puts "======================"
    
    results.each_with_index do |result, index|
      product = result[:product]
      match = result[:category_match]
      
      puts "#{index + 1}. #{product[:name]}"
      
      if match
        puts "   ✅ #{match[:category][:name]}"
        puts "   📁 #{match[:category][:full_path]}"
        puts "   📊 Score: #{match[:score]}"
      else
        puts "   ❌ No category found"
      end
      puts
    end
    
    # Generate report
    report = matcher.generate_assignment_report(results)
    
    puts "Summary:"
    puts "========"
    puts "Total products: #{report[:total_products]}"
    puts "Successfully categorized: #{report[:successfully_categorized]}"
    puts "Failed: #{report[:failed_categorizations]}"
    puts "Success rate: #{report[:success_rate]}%"
    
    puts "\nConfidence distribution:"
    puts "High: #{report[:confidence_distribution][:high]}"
    puts "Medium: #{report[:confidence_distribution][:medium]}"
    puts "Low: #{report[:confidence_distribution][:low]}"
  end
  
  desc "Show category details"
  task :category_info, [:category_id] => :environment do |t, args|
    category_id = args[:category_id]
    
    if category_id.blank?
      puts "Usage: rake avito:category_info[category_id]"
      exit 1
    end
    
    loader = AvitoDataLoader.instance
    loader.ensure_fresh_data
    
    category = loader.get_category(category_id)
    attributes = loader.get_category_attributes(category_id)
    
    if category.nil?
      puts "Category #{category_id} not found"
      exit 1
    end
    
    puts "Category Information:"
    puts "===================="
    puts "ID: #{category[:id]}"
    puts "Name: #{category[:name]}"
    puts "Full Path: #{category[:full_path]}"
    puts "URL: #{category[:url]}"
    
    if attributes
      puts "\nAttributes:"
      puts "==========="
      
      if attributes['attributes']
        attributes['attributes'].each do |attr|
          puts "\n#{attr['tag_name']}"
          puts "  Group: #{attr['group']}"
          puts "  Conditions: #{attr['conditions']&.join(', ')}"
          puts "  Type: #{attr['value_type']}"
          puts "  Description: #{attr['description']}" if attr['description']
          puts "  Example: #{attr['example']}" if attr['example']
          
          if attr['values']
            puts "  Values: #{attr['values'].first(5).join(', ')}"
            puts "          ... and #{attr['values'].count - 5} more" if attr['values'].count > 5
          end
        end
      end
    else
      puts "\nNo attributes data available for this category"
    end
  end
  
  desc "Export category mappings to CSV"
  task export_mappings: :environment do
    require 'csv'
    
    loader = AvitoDataLoader.instance
    loader.ensure_fresh_data
    
    filename = "avito_categories_#{Time.current.strftime('%Y%m%d_%H%M%S')}.csv"
    
    CSV.open(filename, 'w') do |csv|
      csv << ['ID', 'Name', 'Full Path', 'URL', 'Has Attributes']
      
      loader.categories.each do |id, category|
        has_attributes = loader.get_category_attributes(id) ? 'Yes' : 'No'
        csv << [
          category[:id],
          category[:name],
          category[:full_path],
          category[:url],
          has_attributes
        ]
      end
    end
    
    puts "Exported #{loader.categories.count} categories to #{filename}"
  end
end
